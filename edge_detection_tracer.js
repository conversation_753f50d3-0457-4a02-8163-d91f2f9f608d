
setTimeout(() => {
    Java.perform(() => {
            
        traceEdgeDetection()
        }
    );
})

// 边缘检测跟踪脚本 - 增强版

// 全局变量，用于标记是否已经设置了钩子
var hooksInstalled = false;

// 设置钩子的主函数 - 入口点
function traceEdgeDetection() {
    if (hooksInstalled) {
        console.log("[!] 钩子已经安装，无需重复操作");
        return;
    }

    console.log("[+] 开始跟踪边缘检测相关函数...");
    
    // 检查 libmagicclean.so 是否已加载
    var magicCleanSo = Process.findModuleByName("libMagicClean.so");
    if (!magicCleanSo) {
        // 尝试其他可能的名称
        magicCleanSo = Process.findModuleByName("libmagicclean.so");
    }
    
    if (magicCleanSo) {
        console.log("[+] 找到 MagicClean 模块，基地址: " + magicCleanSo.base);
        
        // 尝试查找和跟踪 native 函数
        try {
            // 列出导出函数
            var exports = magicCleanSo.enumerateExports();
            console.log("[+] 导出函数数量: " + exports.length);
            console.log("[+] 前10个导出函数:");
            for (var i = 0; i < Math.min(10, exports.length); i++) {
                console.log("    " + exports[i].name + " - " + exports[i].address);
            }
            
            // 查找可能与边缘检测相关的导出函数
            var edgeRelatedExports = [];
            exports.forEach(function(exp) {
                var name = exp.name.toLowerCase();
                if (name.indexOf("edge") !== -1 || 
                    name.indexOf("corner") !== -1 || 
                    name.indexOf("detect") !== -1 || 
                    name.indexOf("clean") !== -1 || 
                    name.indexOf("model") !== -1) {
                    edgeRelatedExports.push(exp);
                }
            });
            console.log("[+] 找到 " + edgeRelatedExports.length + " 个可能与边缘检测相关的导出函数");
            
            // 列出符号
            var symbols = magicCleanSo.enumerateSymbols();
            console.log("[+] 符号数量: " + symbols.length);
            
            // 查找可能与边缘检测相关的符号
            var edgeRelatedSymbols = [];
            symbols.forEach(function(sym) {
                var name = sym.name.toLowerCase();
                if (name.indexOf("edge") !== -1 || 
                    name.indexOf("corner") !== -1 || 
                    name.indexOf("detect") !== -1 || 
                    name.indexOf("clean") !== -1 || 
                    name.indexOf("model") !== -1) {
                    edgeRelatedSymbols.push(sym);
                }
            });
            console.log("[+] 找到 " + edgeRelatedSymbols.length + " 个可能与边缘检测相关的符号");
            
            // 尝试使用 Java 层方法名称反向查找 native 函数
            console.log("[+] 尝试使用 Java 层方法名称反向查找 native 函数");
            
            // 尝试跟踪 native 函数
            try {
                // 尝试跟踪 imageHasDarkShadow 和 imageHasGlareAfterTorch 函数
                var CameraCleanLiveEdgeDetectionAndroidShim = Java.use("com.adobe.magic_clean.CameraCleanLiveEdgeDetectionAndroidShim");
                
                // 获取 native 方法
                var nativeMethods = CameraCleanLiveEdgeDetectionAndroidShim.class.getDeclaredMethods();
                for (var i = 0; i < nativeMethods.length; i++) {
                    var method = nativeMethods[i];
                    if (method.toString().indexOf("native") !== -1) {
                        console.log("[+] 找到 native 方法: " + method.toString());
                    }
                }
            } catch (e) {
                console.log("[-] 查找 native 方法时出错: " + e);
            }
        } catch (e) {
            console.log("[-] 查找 native 函数时出错: " + e);
        }
        
        // 跟踪 Java 层相关类和方法
        try {
            var CameraCleanAndroidShim = Java.use("com.adobe.magic_clean.CameraCleanAndroidShim");
            
            // 跟踪 GetCorners Java 方法，并尝试获取更多信息
            CameraCleanAndroidShim.GetCorners.implementation = function(input, output) {
                console.log("[+] 调用 Java 层 CameraCleanAndroidShim.GetCorners()");
                
                // 尝试获取输入参数信息
                try {
                    if (input) {
                        console.log("[+] 输入参数信息:");
                        console.log("    图像宽度: " + input.mWidth.value);
                        console.log("    图像高度: " + input.mHeight.value);
                    }
                } catch (e) {
                    console.log("[-] 获取输入参数信息时出错: " + e);
                }
                
                var result = this.GetCorners(input, output);
                console.log("[+] Java GetCorners 返回: " + result);
                
                // 尝试获取输出参数信息
                try {
                    if (output && output.mCCornersInfoVec) {
                        console.log("[+] 输出参数信息:");
                        console.log("    检测到的角点数量: " + output.mCCornersInfoVec.length);
                        
                        // 打印第一组角点信息
                        if (output.mCCornersInfoVec.length > 0) {
                            var corners = output.mCCornersInfoVec[0];
                            if (corners) {
                                try {
                                    var points = corners.value.getPointsRef();
                                    if (points && points.length > 0) {
                                        console.log("    角点坐标:");
                                        for (var i = 0; i < points.length; i++) {
                                            console.log("      点 " + i + ": (" + points[i].x + ", " + points[i].y + ")");
                                        }
                                    }
                                } catch (e) {
                                    console.log("[-] 获取角点坐标时出错: " + e);
                                }
                            }
                        }
                    }
                } catch (e) {
                    console.log("[-] 获取输出参数信息时出错: " + e);
                }
                
                return result;
            };
            
            // 尝试跟踪 CameraCleanLiveEdgeDetectionAndroidShim 类的方法
            try {
                var CameraCleanLiveEdgeDetectionAndroidShim = Java.use("com.adobe.magic_clean.CameraCleanLiveEdgeDetectionAndroidShim");
                
                // 构造函数
                CameraCleanLiveEdgeDetectionAndroidShim.$init.overload('double').implementation = function(threshold) {
                    console.log("[+] 创建 CameraCleanLiveEdgeDetectionAndroidShim 实例，阈值: " + threshold);
                    return this.$init(threshold);
                };
                
                // 清理方法
                CameraCleanLiveEdgeDetectionAndroidShim.cleanup.implementation = function() {
                    console.log("[+] 调用 CameraCleanLiveEdgeDetectionAndroidShim.cleanup()");
                    return this.cleanup();
                };
                
                // 设置间隔方法
                CameraCleanLiveEdgeDetectionAndroidShim.setLiveBoundaryCaptureEnableInterval.implementation = function(interval) {
                    console.log("[+] 设置实时边界捕获间隔: " + interval);
                    return this.setLiveBoundaryCaptureEnableInterval(interval);
                };
                
                // 获取实时角点方法
                if (CameraCleanLiveEdgeDetectionAndroidShim.getLiveCornersGray) {
                    CameraCleanLiveEdgeDetectionAndroidShim.getLiveCornersGray.implementation = function(input, output) {
                        console.log("[+] 调用 CameraCleanLiveEdgeDetectionAndroidShim.getLiveCornersGray()");
                        var result = this.getLiveCornersGray(input, output);
                        console.log("[+] getLiveCornersGray 返回: " + result);
                        return result;
                    };
                }
                
                // 分析暗影方法
                if (CameraCleanLiveEdgeDetectionAndroidShim.imageHasDarkShadow) {
                    CameraCleanLiveEdgeDetectionAndroidShim.imageHasDarkShadow.implementation = function(bitmap, points) {
                        console.log("[+] 调用 CameraCleanLiveEdgeDetectionAndroidShim.imageHasDarkShadow()");
                        var result = this.imageHasDarkShadow(bitmap, points);
                        console.log("[+] imageHasDarkShadow 返回: " + result);
                        return result;
                    };
                }
                
                // 分析眩光方法
                if (CameraCleanLiveEdgeDetectionAndroidShim.imageHasGlareAfterTorch) {
                    CameraCleanLiveEdgeDetectionAndroidShim.imageHasGlareAfterTorch.implementation = function(bitmap1, points1, bitmap2, points2) {
                        console.log("[+] 调用 CameraCleanLiveEdgeDetectionAndroidShim.imageHasGlareAfterTorch()");
                        var result = this.imageHasGlareAfterTorch(bitmap1, points1, bitmap2, points2);
                        console.log("[+] imageHasGlareAfterTorch 返回: " + result);
                        return result;
                    };
                }
                
                console.log("[+] 成功安装 CameraCleanLiveEdgeDetectionAndroidShim 方法钩子");
            } catch (e) {
                console.log("[-] 钩住 CameraCleanLiveEdgeDetectionAndroidShim 类时出错: " + e);
            }
            
            // 尝试跟踪 CameraCleanUtils 类的方法
            try {
                var CameraCleanUtils = Java.use("com.adobe.magic_clean.CameraCleanUtils");
                
                // 预加载模型方法
                if (CameraCleanUtils.preLoadMCModel) {
                    CameraCleanUtils.preLoadMCModel.implementation = function(modelType, useGPU) {
                        console.log("[+] 调用 Java 层 CameraCleanUtils.preLoadMCModel()");
                        console.log("[+] 模型类型: " + modelType + ", 使用GPU: " + useGPU);
                        var result = this.preLoadMCModel(modelType, useGPU);
                        console.log("[+] preLoadMCModel 返回");
                        return result;
                    };
                }
                
                // 设置模型路径方法
                if (CameraCleanUtils.setBasePathToModelsNative) {
                    CameraCleanUtils.setBasePathToModelsNative.implementation = function(path) {
                        console.log("[+] 调用 Java 层 CameraCleanUtils.setBasePathToModelsNative()");
                        console.log("[+] 路径: " + path);
                        var result = this.setBasePathToModelsNative(path);
                        console.log("[+] setBasePathToModelsNative 返回");
                        return result;
                    };
                }
                
                // 从YUV缓冲区获取RGBA位图方法
                if (CameraCleanUtils.getRGBABitmapFromYUVBuffer) {
                    CameraCleanUtils.getRGBABitmapFromYUVBuffer.implementation = function(buffer, width, height) {
                        console.log("[+] 调用 Java 层 CameraCleanUtils.getRGBABitmapFromYUVBuffer()");
                        console.log("[+] 宽度: " + width + ", 高度: " + height);
                        var result = this.getRGBABitmapFromYUVBuffer(buffer, width, height);
                        console.log("[+] getRGBABitmapFromYUVBuffer 返回");
                        return result;
                    };
                }
                
                console.log("[+] 成功安装 CameraCleanUtils 方法钩子");
            } catch (e) {
                console.log("[-] 钩住 CameraCleanUtils 类时出错: " + e);
            }
            
            // 尝试跟踪 EdgeDetectionInput 和 EdgeDetectionOutput 类
            try {
                var EdgeDetectionInput = Java.use("com.adobe.magic_clean.CameraCleanUtils$EdgeDetectionInput");
                var EdgeDetectionOutput = Java.use("com.adobe.magic_clean.CameraCleanUtils$EdgeDetectionOutput");
                
                // 跟踪 EdgeDetectionInput 构造函数
                EdgeDetectionInput.$init.implementation = function() {
                    console.log("[+] 创建 EdgeDetectionInput 实例");
                    return this.$init();
                };
                
                // 跟踪 EdgeDetectionOutput 构造函数
                EdgeDetectionOutput.$init.implementation = function() {
                    console.log("[+] 创建 EdgeDetectionOutput 实例");
                    return this.$init();
                };
                
                console.log("[+] 成功安装 EdgeDetection 类方法钩子");
            } catch (e) {
                console.log("[-] 钩住 EdgeDetection 类时出错: " + e);
            }
            
            // 尝试查找其他可能的边缘检测类
            try {
                console.log("[+] 尝试查找其他可能的边缘检测类...");
                
                // 尝试查找 com.adobe.scan 包下的类
                Java.enumerateLoadedClasses({
                    onMatch: function(className) {
                        if (className.indexOf("com.adobe") !== -1 && 
                            (className.toLowerCase().indexOf("edge") !== -1 || 
                             className.toLowerCase().indexOf("corner") !== -1 || 
                             className.toLowerCase().indexOf("detect") !== -1 || 
                             className.toLowerCase().indexOf("clean") !== -1 || 
                             className.toLowerCase().indexOf("model") !== -1)) {
                            console.log("[+] 找到可能的边缘检测相关类: " + className);
                        }
                    },
                    onComplete: function() {}
                });
            } catch (e) {
                console.log("[-] 查找其他边缘检测类时出错: " + e);
            }
            
            console.log("[+] 成功安装 Java 方法钩子");
            hooksInstalled = true;
        } catch (e) {
            console.log("[-] 钩住 Java 类时出错: " + e);
        }
    } else {
        console.log("[-] 未找到 MagicClean 模块，请确保应用已完全启动");
    }
}

// 列出所有加载的模块
function listModules() {
    console.log("[+] 列出所有已加载的模块:");
    Process.enumerateModules().forEach(function(module) {
        console.log(module.name + " - " + module.base);
    });
}

// 搜索特定模块
function findModule(pattern) {
    console.log("[+] 搜索模块名包含 '" + pattern + "' 的模块:");
    var found = false;
    Process.enumerateModules().forEach(function(module) {
        if (module.name.toLowerCase().indexOf(pattern.toLowerCase()) !== -1) {
            console.log(module.name + " - " + module.base);
            found = true;
        }
    });
    if (!found) {
        console.log("[-] 未找到包含 '" + pattern + "' 的模块");
    }
}

// 查找特定模块的导出函数
function findExports(moduleName) {
    var module = Process.findModuleByName(moduleName);
    if (!module) {
        console.log("[-] 未找到模块: " + moduleName);
        return;
    }
    
    console.log("[+] 列出模块 " + moduleName + " 的导出函数:");
    var exports = module.enumerateExports();
    exports.forEach(function(exp) {
        console.log(exp.name + " - " + exp.address);
    });
}

// 查找模型文件
function findModelFiles() {
    console.log("[+] 尝试查找模型文件...");
    
    // 常见的模型文件路径
    var paths = [
        "/data/data/com.adobe.scan.android/files",
        "/data/data/com.adobe.scan.android/cache",
        "/data/data/com.adobe.scan.android/app_models",
        "/storage/emulated/0/Android/data/com.adobe.scan.android/files",
        "/storage/emulated/0/Android/data/com.adobe.scan.android/cache"
    ];
    
    paths.forEach(function(path) {
        try {
            console.log("[+] 检查路径: " + path);
            var files = Java.use("java.io.File").$new(path).listFiles();
            
            if (files) {
                console.log("[+] 找到 " + files.length + " 个文件/目录");
                
                for (var i = 0; i < files.length; i++) {
                    var file = files[i];
                    var fileName = file.getName();
                    var isDir = file.isDirectory();
                    var fileSize = file.length();
                    
                    console.log("    " + (isDir ? "[目录] " : "[文件] ") + fileName + (isDir ? "" : " (" + fileSize + " 字节)"));
                    
                    // 如果是目录，递归查找
                    if (isDir) {
                        var subFiles = file.listFiles();
                        if (subFiles) {
                            for (var j = 0; j < subFiles.length; j++) {
                                var subFile = subFiles[j];
                                var subFileName = subFile.getName();
                                var subIsDir = subFile.isDirectory();
                                var subFileSize = subFile.length();
                                
                                console.log("        " + (subIsDir ? "[目录] " : "[文件] ") + subFileName + (subIsDir ? "" : " (" + subFileSize + " 字节)"));
                                
                                // 检查是否为模型文件
                                if (!subIsDir && (subFileName.endsWith(".tflite") || 
                                                 subFileName.endsWith(".pb") || 
                                                 subFileName.endsWith(".onnx") || 
                                                 subFileName.endsWith(".model") || 
                                                 subFileName.endsWith(".bin"))) {
                                    console.log("[!] 可能的模型文件: " + path + "/" + fileName + "/" + subFileName);
                                }
                            }
                        }
                    }
                    
                    // 检查是否为模型文件
                    if (!isDir && (fileName.endsWith(".tflite") || 
                                  fileName.endsWith(".pb") || 
                                  fileName.endsWith(".onnx") || 
                                  fileName.endsWith(".model") || 
                                  fileName.endsWith(".bin"))) {
                        console.log("[!] 可能的模型文件: " + path + "/" + fileName);
                    }
                }
            } else {
                console.log("[-] 无法列出文件或目录为空");
            }
        } catch (e) {
            console.log("[-] 访问路径时出错: " + e);
        }
    });
}

console.log("[+] 边缘检测跟踪脚本已加载");
console.log("[+] 调用 traceEdgeDetection() 开始跟踪");
console.log("[+] 调用 listModules() 列出所有已加载的模块");
console.log("[+] 调用 findModule('magic') 搜索特定模块");
console.log("[+] 调用 findExports('libMagicClean.so') 列出模块的导出函数");
console.log("[+] 调用 findModelFiles() 查找模型文件");
